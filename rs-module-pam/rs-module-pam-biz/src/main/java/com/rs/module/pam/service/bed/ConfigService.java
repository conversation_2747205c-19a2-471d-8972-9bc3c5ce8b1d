package com.rs.module.pam.service.bed;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.pam.controller.admin.bed.vo.ChangeSaveRespVO;
import com.rs.module.pam.controller.admin.bed.vo.ConfigRespVO;
import com.rs.module.pam.controller.admin.bed.vo.ConfigSaveReqVO;
import com.rs.module.pam.controller.admin.bed.vo.PrisonerChangePageReqVO;
import com.rs.module.pam.entity.bed.ConfigDO;
import com.rs.module.pam.entity.bed.PrisonerChangeDO;

import javax.validation.Valid;

/**
 * 监所事务管理-监室床位配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ConfigService extends IBaseService<ConfigDO>{

    /**
     * 创建监所事务管理-监室床位配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    ConfigRespVO createConfig(@Valid ConfigSaveReqVO createReqVO);

    /**
     * 监室床位数据初始化
     * @param orgCode
     * @param roomId
     * @return
     */
    ConfigDO createDefaultConfig(String orgCode, String roomId);

    /**
     * 删除监所事务管理-监室床位配置
     *
     * @param id 编号
     */
    void deleteConfig(String id);

    /**
     * 删除监所事务管理-监室床位配置
     *
     * @param orgCode
     * @param roomId
     */
    void deleteConfig(String orgCode, String roomId);

    /**
     * 获得监所事务管理-监室床位配置
     *
     * @param roomId 监室编号
     * @return 监所事务管理-监室床位配置
     */
    ConfigRespVO getByRoomId(String orgCode, String roomId);

    /**
     * 获取监室关押人员
     * @param roomId 监室编号
     * @return
     */
    JSONObject getPrisoner(String orgCode, String roomId);

    /**
     * 获得人员床位调整分页
     *
     * @param pageReqVO 分页查询
     * @return 获得人员床位调整分页
     */
    PageResult<PrisonerChangeDO> getChangePage(PrisonerChangePageReqVO pageReqVO);

    /**
     * 监室人员床位调整
     *
     * @param saveReqVO
     */
    void roomBedChange(ChangeSaveRespVO saveReqVO);

    /***
     * 自动床位-按配置
     * @param orgCode
     * @param roomId
     * @return
     */
    ConfigRespVO autoBedByConfig(String orgCode, String roomId);

}
