package com.rs.module.pam.review;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.CollectionUtil;
import com.rs.module.pam.controller.admin.duty.vo.DutyListRespVO;
import com.rs.module.pam.controller.admin.duty.vo.DutyPrisonerVO;
import com.rs.module.pam.controller.admin.duty.vo.DutySaveVO;
import com.rs.module.pam.controller.admin.duty.vo.DutyVO;
import com.rs.module.pam.entity.duty.RoomAutoConfigDO;
import com.rs.module.pam.service.duty.DutyService;
import com.rs.module.pam.service.duty.RoomAutoConfigService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 监室排班任务
 *
 */
@Component
@Slf4j
public class DutyJob {

    @Resource
    private DutyService dutyService;
    @Resource
    private RoomAutoConfigService roomAutoConfigService;

    /**
     * 数据变动变动检测任务
     */
    @XxlJob("dutyJob")
    @Transactional
    public void dutyJob() {
        List<RoomAutoConfigDO> configDOList = roomAutoConfigService.list(new LambdaQueryWrapper<RoomAutoConfigDO>()
                .eq(RoomAutoConfigDO::getIsEnabled, 1));
        for (RoomAutoConfigDO roomAutoConfigDO : configDOList) {
            String orgCode = roomAutoConfigDO.getOrgCode();
            String roomId = roomAutoConfigDO.getRoomId();
            Date now = new Date();
            DateTime startDate = DateUtil.beginOfWeek(now);
            DateTime endDate = DateUtil.endOfWeek(now);
            DutyVO dutyVO = new DutyVO();
            XxlJobHelper.log(orgCode + "-" + roomId);
            try{
                List<DutyVO> dutyList = dutyService.autoShift(orgCode, roomId, startDate, endDate);
                if (CollectionUtil.isNotNull(dutyList)) {
                    dutyVO = dutyList.get(0);
                }
            } catch (Exception e) {
                XxlJobHelper.log("生成排班数据失败：" + e.getMessage());
                continue;
            }

            List<DutyListRespVO> dutyList = dutyVO.getDutyList();
            List<DutyPrisonerVO> personerList = dutyList.stream()
                    .flatMap(a -> a.getDutyInfo()
                            .stream()
                            .flatMap(b -> b.getPrisonerList().stream()))
                    .collect(Collectors.toList());

            if (personerList.size() > 0) {
                DutySaveVO dutySaveVO = new DutySaveVO();
                dutySaveVO.setOrgCode(orgCode);
                dutySaveVO.setRoomId(roomId);
                dutySaveVO.setDutyList(dutyList);
                try {
                    dutyService.create(dutySaveVO, true);
                } catch (Exception e) {
                    XxlJobHelper.log(orgCode + "-" + roomId + "排班添加失败：" + e.getMessage());
                }
                XxlJobHelper.log(orgCode + "-" + roomId + "排班生成成功！");
            }

        }
    }


}
