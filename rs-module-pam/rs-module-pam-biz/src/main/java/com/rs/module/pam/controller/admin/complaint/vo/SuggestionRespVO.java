package com.rs.module.pam.controller.admin.complaint.vo;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.util.Date;

@ApiModel(description = "管理后台 - 监所事务管理-投诉建议 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SuggestionRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("监管人员编码")
    private String jgrybm;
    @ApiModelProperty("监管人员姓名")
    private String jgryxm;
    @ApiModelProperty("监室ID")
    private String roomId;
    @ApiModelProperty("监室名称")
    private String roomName;
    @ApiModelProperty("投诉类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_TSJYLX")
    private String complaintType;
    @ApiModelProperty("投诉内容")
    private String complaintContent;
    @ApiModelProperty("处理状态")
    private String handleStatus;
    @ApiModelProperty("处理人身份证号")
    private String handleUserSfzh;
    @ApiModelProperty("处理人姓名")
    private String handleUserName;
    @ApiModelProperty("处理反馈")
    private String handleFeedback;

    private String cityName;

    private String cityCode;

    private String regName;

    private String regCode;

    private String orgName;

    private String orgCode;

    private Boolean isDel;

    private String addUser;

    private String addUserName;

    @ApiModelProperty("申请时间")
    private Date addTime;

    private String updateUser;

    private String updateUserName;

    private Date updateTime;
}
