package com.rs.module.pam.service.duty;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.google.common.collect.Lists;
import com.gosun.zhjg.common.constant.SocketActionConstants;
import com.gosun.zhjg.common.util.DateUtils;
import com.gosun.zhjg.prison.room.terminal.modules.socket.dto.PushMessageForm;
import com.gosun.zhjg.prison.room.terminal.modules.socket.feign.SocketPushFeign;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.pam.controller.admin.duty.vo.DutyShiftSaveReqVO;
import com.rs.module.pam.controller.admin.duty.vo.ShiftRespVO;
import com.rs.module.pam.controller.admin.duty.vo.ShiftSaveReqVO;
import com.rs.module.pam.dao.duty.DutyDao;
import com.rs.module.pam.dao.duty.RecordsDao;
import com.rs.module.pam.dao.duty.ShiftDao;
import com.rs.module.pam.entity.duty.ShiftDO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 监所事务管理-值班班次 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ShiftServiceImpl extends BaseServiceImpl<ShiftDao, ShiftDO> implements ShiftService {

    @Resource
    private DutyDao dutyDao;
    @Resource
    private RecordsDao recordsDao;
    @Resource
    private ShiftDao shiftDao;
    @Resource
    private SocketPushFeign socketPushFeign;
    @Resource
    private BspApi bspApi;

    @Override
    public void shiftManageSave(DutyShiftSaveReqVO reqVO) {
        List<ShiftSaveReqVO> shiftList = reqVO.getShiftList();
        String roomId = reqVO.getRoomId();
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        // 校验是保存还是编辑
        List<ShiftDO> dbShiftList = shiftDao.getEffectiveShift(sessionUser.getOrgCode(), reqVO.getRoomId());

        // 是否已经到了生效时间，没有到生效日期视为同一批次
        boolean effectiveStart = dbShiftList.isEmpty() || dbShiftList.get(0).getEffectiveStartDate().getTime() <= System.currentTimeMillis();
        // 班次名变动。记录出id后面进行更新
        List<String> shiftNameChangeIdList = new ArrayList<>();
        // 新旧交集的id。用于删除之前有现在没有的班次
        List<String> intersectIdList = new ArrayList<>();
        // 是否有版本变动。 修改了开始结束时间， 或增加和删除条目，认为是版本变动
        boolean versionChange = false;
        Date tomorrowDate = new Date(DateUtils.startOfDay().getTime() + 86400000L);
        // 本次新增的班次
        List<ShiftDO> insertList = new ArrayList<>();
        for (ShiftSaveReqVO shift : shiftList) {
            Optional<ShiftDO> optional = dbShiftList.stream().filter(f ->
                    ObjectUtils.equals(shift.getStartTime(), f.getStartTime()) &&
                            ObjectUtils.equals(shift.getEndTime(), f.getEndTime())).findFirst();
            if (optional.isPresent()) {
                ShiftDO obj = optional.get();
                intersectIdList.add(obj.getId());
                shift.setId(obj.getId());
                if (!ObjectUtils.equals(shift.getShiftName(), obj.getShiftName())) {
                    shiftNameChangeIdList.add(obj.getId());
                }
            } else {
                versionChange = true;
                insertList.add(BeanUtils.toBean(shift, ShiftDO.class));
            }
        }
        versionChange = versionChange || intersectIdList.size() != dbShiftList.size();
        if (versionChange && effectiveStart) {
            // 增加新班次
            for (ShiftSaveReqVO shift : shiftList) {
                ShiftDO entity = new ShiftDO();
                entity.setRoomId(roomId);
                entity.setStartTime(shift.getStartTime());
                entity.setEndTime(shift.getEndTime());
                entity.setShiftName(shift.getShiftName());
                entity.setSort(shift.getSort());
                // 明天开始时间。明天有效
                entity.setEffectiveStartDate(tomorrowDate);
                entity.setStartTimeType(shift.getStartTimeType());
                entity.setEndTimeType(shift.getEndTimeType());
                shiftDao.insert(entity);
            }
            // 将旧的 有效日期回填上。方便后续使用
            if (!dbShiftList.isEmpty()) {
                List<String> ids = dbShiftList.stream().map(ShiftDO::getId).collect(Collectors.toList());
                ShiftDO shiftDO = new ShiftDO();
                shiftDO.setEffectiveEndDate(tomorrowDate);
                shiftDao.update(shiftDO, new LambdaQueryWrapper<ShiftDO>().in(ShiftDO::getId, ids));
            }
        }
        if (!versionChange || !effectiveStart) {
            // 删除之前有，现在没有的
            List<String> collect = dbShiftList.stream().filter(f -> !intersectIdList.contains(f.getId())).map(ShiftDO::getId).collect(Collectors.toList());
            if (!collect.isEmpty()) {
                shiftDao.deleteBatchIds(collect);
            }
            // 更新
            for (ShiftSaveReqVO dto : shiftList) {
                if (shiftNameChangeIdList.contains(dto.getId())) {
                    ShiftDO entity = new ShiftDO();
                    entity.setId(dto.getId());
                    entity.setShiftName(dto.getShiftName());
                    entity.setSort(dto.getSort());
                    entity.setStartTimeType(dto.getStartTimeType());
                    entity.setEndTimeType(dto.getEndTimeType());
                    shiftDao.updateById(entity);
                }
            }
            // 插入
            for (ShiftDO shift : insertList) {
                // 本次有新增的部分
                ShiftDO entity = new ShiftDO();
                entity.setRoomId(roomId);
                entity.setStartTime(shift.getStartTime());
                entity.setEndTime(shift.getEndTime());
                entity.setShiftName(shift.getShiftName());
                entity.setSort(shift.getSort());
                Date effectiveStartDate = dbShiftList.get(0).getEffectiveStartDate();
                entity.setEffectiveStartDate(effectiveStartDate);
                entity.setStartTimeType(shift.getStartTimeType());
                entity.setEndTimeType(shift.getEndTimeType());
                shiftDao.insert(entity);
            }
        }
        // 任何修改 都将删除后面排班
        if (versionChange) {
            recordsDao.deleteByDutyDate(sessionUser.getOrgCode(), roomId, tomorrowDate);
            dutyDao.cleanTablePrisonRoomDuty(sessionUser.getOrgCode(), roomId, tomorrowDate);
        }

        // 检测到其他民警对值班班次进行了更改，将刷新界面”，此时出现一个按钮“确认
        PushMessageForm form = new PushMessageForm();
        form.setAction(SocketActionConstants.web_dutyShiftChange);
        form.setPrisonIds(Lists.newArrayList(sessionUser.getOrgCode()));
        form.setUserIdBlacklist(Lists.newArrayList(sessionUser.getId()));
        form.setTerminal(SocketActionConstants.PushMessageTerminalEnum.PLATFORM.name());
        try {
            socketPushFeign.pushMessageToPrison(form);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        // 班次变动通知仓内屏
    }

    @Override
    public List<ShiftRespVO> getShift(String orgCode, String roomId) {
        List<ShiftDO> effectiveShift = shiftDao.getEffectiveShift(orgCode, roomId);
        if (CollectionUtil.isEmpty(effectiveShift)) {
            effectiveShift = shiftDao.createDefaultShift(orgCode, roomId);
        }
        effectiveShift.sort(Comparator.comparing(ShiftDO::getSort));
        return BeanUtils.toBean(effectiveShift, ShiftRespVO.class);
    }


}
