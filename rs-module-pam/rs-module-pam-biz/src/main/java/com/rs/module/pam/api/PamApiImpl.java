package com.rs.module.pam.api;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.rs.adapter.bsp.api.dto.*;
import com.rs.module.pam.dao.duty.ShiftDao;
import com.rs.module.pam.dao.duty.day.DayDutyShiftDao;
import com.rs.module.pam.service.bed.ConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * RPC服务-API接口实现类，提供RESTful API接口，给Feign调用
 *
 * <AUTHOR>
 * @date 2025年4月14日
 */
@RestController
@Validated
@Api(tags = "pam - api服务")
public class PamApiImpl implements PamApi {
    private static final Logger log = LoggerFactory.getLogger(PamApiImpl.class);

    @Resource
    private ConfigService configService;
    @Resource
    private ShiftDao shiftDao;
    @Resource
    private DayDutyShiftDao dayDutyShiftDao;

    @ApiOperation("监室床位数据初始化")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号", required = true),
            @ApiImplicitParam(name = "roomId", value = "监室编号", required = true)
    })
    @Override
    public void createDefaultConfig(String orgCode, String roomId) {
        configService.createDefaultConfig(orgCode, roomId);
    }

    @ApiOperation("删除监室床位数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号", required = true),
            @ApiImplicitParam(name = "roomId", value = "监室编号", required = true)
    })
    @Override
    public void delConfig(String orgCode, String roomId) {
        configService.deleteConfig(orgCode, roomId);
    }



    @ApiOperation("监室值班班次数据初始化")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号", required = true),
            @ApiImplicitParam(name = "roomId", value = "监室编号", required = true)
    })
    @Override
    public void createDefaultShift(String orgCode, String roomId) {
        shiftDao.createDefaultShift(orgCode, roomId);
    }

    @ApiOperation("删除监室值班班次数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号", required = true),
            @ApiImplicitParam(name = "roomId", value = "监室编号", required = true)
    })
    @Override
    public void delShift(String orgCode, String roomId) {
        shiftDao.deleteShift(orgCode, roomId);
    }



    @ApiOperation("监室值日班次数据初始化")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号", required = true),
            @ApiImplicitParam(name = "roomId", value = "监室编号", required = true)
    })
    @Override
    public void createDefaultDayShift(String orgCode, String roomId) {
        dayDutyShiftDao.createDefaultShift(orgCode, roomId);
    }

    @ApiOperation("删除监室值日班次数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgCode", value = "机构编号", required = true),
            @ApiImplicitParam(name = "roomId", value = "监室编号", required = true)
    })
    @Override
    public void delDayShift(String orgCode, String roomId) {
        dayDutyShiftDao.deleteShift(orgCode, roomId);
    }

}
