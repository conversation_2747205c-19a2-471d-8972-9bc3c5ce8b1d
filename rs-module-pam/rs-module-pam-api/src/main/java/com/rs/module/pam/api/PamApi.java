package com.rs.module.pam.api;

import com.rs.module.pam.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC服务-BSP API")
public interface PamApi {

	String PREFIX = ApiConstants.PREFIX + "/api";

	@PostMapping(PREFIX + "/bed/createDefaultConfig")
	@Operation(summary = "监室床位数据初始化")
	@Parameters({
			@Parameter(name = "orgCode", description = "机构编号", required = true),
			@Parameter(name = "roomId", description = "监室编号", required = true)
	})
	public void createDefaultConfig(@RequestParam("orgCode") String orgCode,
									@RequestParam("roomId") String roomId);

	@PostMapping(PREFIX + "/bed/delConfig")
	@Operation(summary = "删除监室床位数据")
	@Parameters({
			@Parameter(name = "orgCode", description = "机构编号", required = true),
			@Parameter(name = "roomId", description = "监室编号", required = true)
	})
	public void delConfig(@RequestParam("orgCode") String orgCode,
						  @RequestParam("roomId") String roomId);



	@PostMapping(PREFIX + "/dutyShift/createDefaultShift")
	@Operation(summary = "监室值班班次数据初始化")
	@Parameters({
		@Parameter(name = "orgCode", description = "机构编号", required = true),
		@Parameter(name = "roomId", description = "监室编号", required = true)
	})
	public void createDefaultShift(@RequestParam("orgCode") String orgCode,
								   @RequestParam("roomId") String roomId);

	@PostMapping(PREFIX + "/dutyShift/delShift")
	@Operation(summary = "删除监室值班班次数据")
	@Parameters({
			@Parameter(name = "orgCode", description = "机构编号", required = true),
			@Parameter(name = "roomId", description = "监室编号", required = true)
	})
	public void delShift(@RequestParam("orgCode") String orgCode,
						 @RequestParam("roomId") String roomId);



	@PostMapping(PREFIX + "/dayDutyShift/createDefaultDayShift")
	@Operation(summary = "监室值日班次数据初始化")
	@Parameters({
			@Parameter(name = "orgCode", description = "机构编号", required = true),
			@Parameter(name = "roomId", description = "监室编号", required = true)
	})
	public void createDefaultDayShift(@RequestParam("orgCode") String orgCode,
								   @RequestParam("roomId") String roomId);


	@PostMapping(PREFIX + "/dayDutyShift/delDayShift")
	@Operation(summary = "删除监室值日班次数据")
	@Parameters({
			@Parameter(name = "orgCode", description = "机构编号", required = true),
			@Parameter(name = "roomId", description = "监室编号", required = true)
	})
	public void delDayShift(@RequestParam("orgCode") String orgCode,
							@RequestParam("roomId") String roomId);
}
