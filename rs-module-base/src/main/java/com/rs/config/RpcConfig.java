package com.rs.config;

import com.rs.adapter.bsp.api.BpmApi;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.BspSdk;
import com.rs.adapter.bsp.api.UserApi;
import com.rs.module.pam.api.PamApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * RPC远程访问配置类
 * <AUTHOR>
 * @date 2025年4月13日
 */
@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {UserApi.class, BspApi.class, BspSdk.class, BpmApi.class, PamApi.class})
public class RpcConfig {

}
