package com.gosun.zhjg.prison.room.terminal.modules.inscreen.controller;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.gosun.zhjg.basic.business.modules.file.controller.TerminalVersionManagementFileController;
import com.gosun.zhjg.basic.business.modules.file.feign.FileApi;
import com.gosun.zhjg.common.exception.BaseException;
import com.gosun.zhjg.common.msg.R;
import com.gosun.zhjg.common.util.CommonServiceUtils;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.constant.TerminalVersionManagementConstants;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.BaseDeviceInscreenDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.TerminalVersionManagementDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dao.jsyw.TerminalVersionUpgradeLogDao;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.dto.*;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.BaseDeviceInscreenEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.TerminalVersionInfoEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.TerminalVersionManagementEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.entity.TerminalVersionUpgradeLogEntity;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.TerminalService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.TerminalVersionManagementFileScanJob;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.service.TerminalVersionManagementService;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.TerminalVersionInfoVO;
import com.gosun.zhjg.prison.room.terminal.modules.inscreen.vo.TerminalVersionUpgradeLogVO;
import com.gosun.zhjg.prison.room.terminal.modules.socket.service.SocketService;
import com.rs.module.base.vo.DeleteRequestBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URI;
import java.util.*;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

/**
 * 终端版本管理及版本升级运维相关功能
 *
 * <AUTHOR>
 * @date 2023/8/18 9:53
 */
@Slf4j
@Api(tags = "终端版本管理及版本升级运维相关功能")
@RequestMapping("inscreen/terminalversionmanagement")
@RestController
public class TerminalVersionManagementController {

    @Autowired
    private TerminalVersionManagementFileScanJob terminalVersionManagementFileScanJob;
    @Autowired(required = false)
    private FileApi fileApi;
    @Value("${file-url-prefix}")
    private String pathPrefix;
    @Autowired
    private TerminalVersionManagementDao terminalVersionManagementDao;
    @Autowired
    private SocketService socketService;
    @Autowired
    private TerminalVersionUpgradeLogDao terminalVersionUpgradeLogDao;
    @Autowired
    private BaseDeviceInscreenDao baseDeviceInscreenDao;
    @Autowired
    private TerminalService terminalService;
    @Autowired
    private DiscoveryClient discoveryClient;
    @Autowired
    private TerminalVersionManagementService terminalVersionManagementService;

    @Autowired
    private FileStorageService fileStorageService;

    @RequestMapping(value = "/sync", method = RequestMethod.GET)
    @ApiOperation(value = "同步-扫描文件到表")
    public R<?> sync() {
        terminalVersionManagementFileScanJob.run();
        return R.ResponseOk();
    }

    /**
     * 上传升级包
     * 废弃。上传速度会慢一些
     * 使用 {@link TerminalVersionManagementFileController}
     *
     * @param file
     * @return
     */
    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/deprecated/upload", method = RequestMethod.POST)
    @ApiOperation(value = "上传文件")
    public R<?> upload(@RequestParam("file") MultipartFile file) {
        String fileName = file.getOriginalFilename();
        Matcher matcher = TerminalVersionManagementConstants.Package_Pattern.matcher(fileName);
        if (!matcher.matches()) {
            return R.ResponseError(400, "无效的包名");
        }
        TerminalVersionManagementEntity entity = TerminalVersionManagementFileScanJob.parseFileName2Entity(fileName);
        // 检测改包是否已经存在
        List<TerminalVersionManagementEntity> list = terminalVersionManagementDao.selectList(new LambdaQueryWrapper<TerminalVersionManagementEntity>()
                .eq(TerminalVersionManagementEntity::getPackageName, fileName)
                .orderByAsc(TerminalVersionManagementEntity::getAddTime)
        ).stream().collect(Collectors.toList());
        if (list.isEmpty()) {
            entity.setId(IdUtil.fastSimpleUUID());
            terminalVersionManagementDao.insert(entity);
        } else {
            // 修改
            for (TerminalVersionManagementEntity e : list) {
                entity.setId(e.getId());
                entity.setUpdateTime(new Date());
                terminalVersionManagementDao.updateById(entity);
            }
        }
        FileInfo fileInfo = fileStorageService.of(file)
                .setHashCalculatorMd5()
                .setName(file.getOriginalFilename())
                .setSaveFilename(file.getOriginalFilename())
                .upload();  //将文件上传到对应地方
        fileInfo.setId(entity.getId());
        entity.setUrl(fileInfo.getUrl());
        terminalVersionManagementDao.updateById(entity);// Set the URL of the file in the entity
        return R.ResponseResult(fileInfo);
    }

    @RequestMapping(value = "/saveByPackageName", method = RequestMethod.POST)
    @ApiOperation(value = "保存-根据文件名")
    public R<?> saveByPackageName(@RequestBody TerminalVersionManagementsDto form) {
        String fileName = form.getPackageName();
        Matcher matcher = TerminalVersionManagementConstants.Package_Pattern.matcher(fileName);
        if (!matcher.matches()) {
            return R.ResponseError(400, "无效的包名");
        }
        TerminalVersionManagementEntity entity = TerminalVersionManagementFileScanJob.parseFileName2Entity(fileName);
        // 检测改包是否已经存在
        List<TerminalVersionManagementEntity> list = terminalVersionManagementDao.selectList(new LambdaQueryWrapper<TerminalVersionManagementEntity>()
                .eq(TerminalVersionManagementEntity::getPackageName, fileName)
                .orderByAsc(TerminalVersionManagementEntity::getAddTime)
        ).stream().collect(Collectors.toList());

        if (list.isEmpty()) {
            entity.setCompetingVersions(form.getCompetingVersions());
            entity.setReleaseNotes(form.getReleaseNotes());
            entity.setUrl(form.getPackageUrl());
            terminalVersionManagementDao.insert(entity);
        } else {
            // 修改
            for (TerminalVersionManagementEntity e : list) {
                entity.setCompetingVersions(form.getCompetingVersions());
                entity.setReleaseNotes(form.getReleaseNotes());
                entity.setId(e.getId());
                entity.setUpdateTime(new Date());
                entity.setUrl(form.getPackageUrl());
                terminalVersionManagementDao.updateById(entity);
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("id", entity.getId());
        return R.ResponseResult(map);
    }

    /**
     * 检查包名是否正确
     *
     * @param form
     * @return 包存放ftp目录
     */
    @RequestMapping(value = "/checkPackageName", method = RequestMethod.POST)
    @ApiOperation(value = "检查包名是否正确")
    public R<?> checkPackageName(@RequestBody TerminalVersionManagementsDto form) {
        String fileName = form.getPackageName();
        Matcher matcher = TerminalVersionManagementConstants.Package_Pattern.matcher(fileName);
        if (!matcher.matches()) {
            return R.ResponseError(400, "无效的包名");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("ok", true);
        // 带出ftp目录
        map.put("packageFolder", TerminalVersionManagementConstants.Package_Folder);
        return R.ResponseResult(map);
    }

    /**
     * 列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ApiOperation(value = "终端版本管理-分页列表", responseContainer = "List", response = TerminalVersionManagementsDto.class)
    public R<?> list(@ApiParam(hidden = true) TerminalVersionManagementPageDto form) {
        Page page = null;
        if (form.getPage() == null || form.getPage()) {
            page = form.trainToPage();
        }
        List<TerminalVersionManagementsDto> records = terminalVersionManagementDao.findByPage(form, page);
        for (TerminalVersionManagementsDto vo : records) {
            vo.setPackageUrl(this.getPackageUrl(vo.getPackageName()));
        }
        if (page == null) {
            page = new Page<>(1, records.size());
            page.setTotal(records.size());
        }
        page.setRecords(records);
        return R.ResponsePage(page);
    }

    /**
     * 信息
     */
    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "终端版本管理-根据编号查找记录", responseContainer = "Map", response = TerminalVersionManagementsDto.class)
    @ApiImplicitParam(paramType = "path", name = "id", value = "编号", required = true, dataType = "String")
    public R<?> info(@PathVariable("id") String id) {
        TerminalVersionManagementsDto vo = terminalVersionManagementDao.findOneById(id);
        if (vo != null) {
            vo.setPackageUrl(this.getPackageUrl(vo.getPackageName()));
        }
        return R.ResponseResult(vo);
    }

    /**
     * 保存
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ApiOperation(value = "终端版本管理-信息保存", responseContainer = "Map")
    public R update(@RequestBody TerminalVersionManagementsDto form) {
        if (StringUtils.isBlank(form.getId())) {
            throw new BaseException("参数异常", 400);
        }
        TerminalVersionManagementEntity entity = new TerminalVersionManagementEntity();
        // 校验版本号是否正确
        Matcher matcher = TerminalVersionManagementConstants.Version_Pattern.matcher(form.getVersionNumber());
        if (StringUtils.isNotBlank(form.getVersionNumber())) {
            if (!matcher.matches()) {
                throw new BaseException("无效的版本号", 400);
            }
            entity.setVersionNumber(form.getVersionNumber());
            entity.setMajorVersion(Integer.valueOf(matcher.group(1)));
            entity.setMinorVersion(Integer.valueOf(matcher.group(2)));
            entity.setPatchVersion(Integer.valueOf(matcher.group(3)));
        }
        if (StringUtils.isNotBlank(form.getCompetingVersions())) {
            if (!TerminalVersionManagementConstants.Competing_Version_Pattern.matcher(form.getCompetingVersions()).matches()) {
                throw new BaseException("无效的兼容版本号", 400);
            }
        }
        entity.setId(form.getId());
        entity.setFixed(form.getFixed());
        entity.setCityAbbr(form.getCityAbbr());
        entity.setSysAbbr(form.getSysAbbr());
        entity.setMachineAbbr(form.getMachineAbbr());
        entity.setCompetingVersions(form.getCompetingVersions());
        entity.setReleaseNotes(form.getReleaseNotes());
        entity.setUpdateTime(new Date());
        terminalVersionManagementDao.updateById(entity);
        return R.ResponseOk("保存成功");
    }

    /**
     * 删除
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ApiOperation(value = "终端版本管理-删除", responseContainer = "Map")
    @ApiImplicitParam(paramType = "body", name = "ids", value = "编号", required = true, allowMultiple = true, dataType = "String")
    public R delete(@RequestBody DeleteRequestBody form) {
        terminalVersionManagementDao.deleteBatchIds(form.getIdList());
        return R.ResponseOk("删除成功");
    }

    /**
     * 根据包名获取ftp访问路径
     *
     * @param packageName
     * @return
     */
    private String getPackageUrl(String packageName) {
        String packageUrl = pathPrefix + TerminalVersionManagementConstants.Package_Folder + "/" + packageName;
        try {
            // 格式化url
            packageUrl = new URI(packageUrl).normalize().toString();
        } catch (Exception ex) {
        }
        return packageUrl;
    }

    /**
     * 查询兼容版本
     *
     * @param form
     * @return
     */
    /*
    @RequestMapping(value = "competingVersions", method = RequestMethod.GET)
    @ApiOperation(value = "终端版本管理-查询兼容版本", responseContainer = "Map")
    public R appCompetingVersions(TerminalVersionManagementPageDto form) {
        String fixed = form.getFixed();
        String sysAbbr = form.getSysAbbr();
        String versionNumber = form.getVersionNumber();
        String competingVersions = form.getCompetingVersions();
        if (StringUtils.isBlank(versionNumber) && StringUtils.isBlank(competingVersions)) {
            return R.ResponseError(400, "参数为空");
        }
        String sysAbbrMap = TerminalVersionManagementConstants.SysAbbrMap.get(sysAbbr);
        if (sysAbbrMap == null) {
            return R.ResponseError(400, "无效的系统缩写");
        }
        if (StringUtils.isBlank(competingVersions)) {
            List<TerminalVersionManagementEntity> aproject = null;
            if (StringUtils.isNotBlank(versionNumber)) {
                aproject = terminalVersionManagementDao.selectList(new LambdaQueryWrapper<TerminalVersionManagementEntity>()
                        .eq(TerminalVersionManagementEntity::getFixed, fixed)
                        .eq(TerminalVersionManagementEntity::getSysAbbr, sysAbbr)
                        .eq(TerminalVersionManagementEntity::getVersionNumber, versionNumber)
                        .orderByDesc(TerminalVersionManagementEntity::getCreateTime));
            }
            if (aproject == null || aproject.isEmpty()) {
                return R.ResponseError(400, "未找到版本包信息");
            }
            // 查询出第一个兼容版本
            for (TerminalVersionManagementEntity entity : aproject) {
                if (StringUtils.isNotBlank(entity.getCompetingVersions())) {
                    competingVersions = entity.getCompetingVersions();
                    break;
                }
            }
        }
        if (competingVersions == null) {
            return R.ResponseError(400, "未找到兼容版本号信息");
        }
        Matcher competingversionMatcher = TerminalVersionManagementConstants.Competing_Version_Pattern.matcher(competingVersions);
        if (!competingversionMatcher.matches()) {
            return R.ResponseError(400, "无效的兼容版本号 " + competingVersions);
        }
        TerminalVersionManagementPageDto form2 = new TerminalVersionManagementPageDto();
        String versionMatchRule = competingVersions.substring(0, 1);
        form2.setVersionMatchRule(versionMatchRule);
        form2.setMajorVersion(Integer.valueOf(competingversionMatcher.group(1)));
        form2.setMinorVersion(Integer.valueOf(competingversionMatcher.group(2)));
        form2.setPatchVersion(Integer.valueOf(competingversionMatcher.group(3)));
        form2.setFixed("FEproject");
        form2.setSysAbbr(sysAbbrMap);
        form2.setState(1);
        List<TerminalVersionManagementsDto> records = terminalVersionManagementDao.findByPage(form2, null);
        for (TerminalVersionManagementsDto vo : records) {
            vo.setPackageUrl(this.getPackageUrl(vo.getPackageName()));
        }
        return R.ResponseResult(records);
    }
    */

    /**
     * 根据兼容版本，查询符合条件的web包
     * 1）安卓传一个兼容模式的版本号，比如是^v1.1.1，这种，然后你返回最大版本号匹配的一个包地址，同时返回web包地址
     * 1）传一个兼容版本号，你返回可以下载的全部web包列表，同时返回返回web包地址
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "selectCompatibleWebVersions", method = RequestMethod.GET)
    @ApiOperation(value = "终端版本管理-查询兼容版本")
    public R selectCompatibleWebVersions(TerminalVersionManagementPageDto form) {
        String competingVersions = form.getCompetingVersions();
        if (StringUtils.isBlank(competingVersions)) {
            throw new BaseException("无效的兼容版本号", 400);
        }

        if (StringUtils.isBlank(form.getFixed())) {
            form.setFixed("FEproject");
        }
        form.setOrderByVersion(true);
        form.setState(1);
        List<TerminalVersionManagementsDto> records = terminalVersionManagementDao.findByPage(form, null);
        for (TerminalVersionManagementsDto vo : records) {
            vo.setPackageUrl(this.getPackageUrl(vo.getPackageName()));
        }
        if (form.getPageSize() != null) {
            records = records.stream().limit(form.getPageSize()).collect(Collectors.toList());
        }
        return R.ResponseResult(records);
    }

    @RequestMapping(value = "terminalVersionList", method = RequestMethod.GET)
    @ApiOperation(value = "终端版本管理-设备版本信息-列表")
    public R terminalVersionList(TerminalVersionInfoPageDto form) {
        Page page = form.trainToPage();
        List<TerminalVersionInfoVO> records = terminalVersionManagementDao.terminalVersionPage(form, page);
        for (TerminalVersionInfoVO vo : records) {
            if (vo.getDeviceType() != null) {
                if (vo.getDeviceType() == 1) {
                    vo.setDeviceTypeDisplayName("仓内屏");
                } else if (vo.getDeviceType() == 2) {
                    vo.setDeviceTypeDisplayName("仓外屏");
                }
            }
            List<String> clientIds = socketService.getSessionBySerialNumber(Lists.newArrayList(vo.getSerialNumber()));
            if (clientIds.isEmpty()) {
                vo.setOnLine(0);
            } else {
                // 1标识在线
                vo.setOnLine(1);
            }
        }
        page.setRecords(records);
        return R.ResponsePage(page);
    }

    @RequestMapping(value = "terminalVersionSync", method = RequestMethod.GET)
    @ApiOperation(value = "终端版本管理-设备版本信息-同步")
    public R terminalVersionSync() {
        new Thread(() -> {
            terminalVersionSync2();
        }).start();
        return R.ResponseOk();
    }

    private void terminalVersionSync2() {
        List<BaseDeviceInscreenEntity> deviceEntities = baseDeviceInscreenDao.selectList(new LambdaQueryWrapper<BaseDeviceInscreenEntity>()
                .select(BaseDeviceInscreenEntity::getSerialNumber, BaseDeviceInscreenEntity::getDeviceIp)
        );
        List<String> allDeviceSerialNumberList = deviceEntities.stream().map(BaseDeviceInscreenEntity::getSerialNumber).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 同步成功的序列号集合
        List<String> ok = new ArrayList<>();
        for (String serialNumber : allDeviceSerialNumberList) {
//            if (!"a0bfffeafada6d30".equals(serialNumber)) {
//                continue;
//            }
            Optional<String> optional = deviceEntities.stream().filter(f -> serialNumber.equals(f.getSerialNumber())).map(BaseDeviceInscreenEntity::getDeviceIp).filter(StringUtils::isNotBlank).findFirst();
            if (!optional.isPresent()) {
                log.warn("设备版本信息-同步>设备{}未找到ip信息", serialNumber);
                continue;
            }
            String ip = optional.get();
            TerminalVersionInfoEntity entity = terminalVersionManagementService.syncVersionInfo(ip);
            if (entity == null) {
                continue;
            }
            ok.add(entity.getSerialNumber());
//region 通过websocket方式通知
            /*
            List serialNumberList = Lists.newArrayList(serialNumber);
            List<String> clientIds = socketService.getSessionBySerialNumber(serialNumberList);
            if (clientIds.isEmpty()) {
                // 设备不在线
                continue;
            }
            PushMessageForm pushFrom = new PushMessageForm(SocketActionConstants.PushMessageTargetEnum.android.name(), SocketActionConstants.systemInfo);
            pushFrom.setSerialNumbers(serialNumberList);
            pushFrom.setSessionIds(clientIds);
            PushMessageAckVO ackVO = socketService.pushMessageToClientWaitReply(pushFrom);
            log.debug("设备版本信息同步  {}", JSON.toJSONString(ackVO));
            Object response;
            if (ackVO.getOk() && (response = ackVO.getResponse()) != null) {
                String responseStr = JSON.toJSONString(response);
                TerminalVersionInfoEntity entity = JSON.parseObject(responseStr, TerminalVersionInfoEntity.class);
                if (entity == null && StringUtils.isNotBlank(entity.getSerialNumber())) {
                    entity.setUpdateTime(new Date());
                    int effectRow = terminalVersionInfoDao.updateById(entity);
                    if (effectRow == 0) {
                        terminalVersionInfoDao.insert(entity);
                    }
                    ok.add(entity.getSerialNumber());
                }
            }
            */
// endregion
        }
        log.info("terminalVersionSync成功集合 ---> {}", JSON.toJSONString(ok));
    }

    @RequestMapping(value = "terminalUpgradeLogList", method = RequestMethod.GET)
    @ApiOperation(value = "终端版本管理-设备版本升级日志-列表")
    public R terminalVersionList(TerminalVersionUpgradeLogPageDto form) {
        Page page = form.trainToPage();
        List<TerminalVersionUpgradeLogVO> records = terminalVersionManagementDao.terminalUpgradeLogPage(form, page);
        for (TerminalVersionUpgradeLogVO vo : records) {
            if (vo.getDeviceType() != null) {
                if (vo.getDeviceType() == 1) {
                    vo.setDeviceTypeDisplayName("仓内屏");
                } else if (vo.getDeviceType() == 2) {
                    vo.setDeviceTypeDisplayName("仓外屏");
                }
            }
        }
        page.setRecords(records);
        return R.ResponsePage(page);
    }

    @RequestMapping(value = "terminalUpgradeLogSave", method = RequestMethod.POST)
    @ApiOperation(value = "终端版本管理-设备版本升级日志-保存")
    public R terminalUpgradeLogSave(@RequestBody TerminalVersionUpgradeLogSaveDto form) {
        TerminalVersionUpgradeLogEntity entity = new TerminalVersionUpgradeLogEntity();
        entity.setId(UUID.randomUUID().toString().replaceAll("-", ""));
        entity.setSerialNumber(form.getSerialNumber());
        entity.setIp(form.getIp());
        entity.setApkVersion(form.getApkVersion());
        entity.setWebVersion(form.getWebVersion());
        entity.setUpgradeType(form.getUpgradeType());
        entity.setUpgradeVersion(form.getUpgradeVersion());
        entity.setOk(form.getOk());
        terminalVersionUpgradeLogDao.insert(entity);
        return R.ResponseOk();
    }

    @RequestMapping(value = "upgrade", method = RequestMethod.POST)
    @ApiOperation(value = "终端版本管理-升级")
    public R terminalVersionList(@RequestBody @Validated TerminalVersionUpgradeDto form) {
        String packageId = form.getPackageId();
        TerminalVersionManagementEntity entity = terminalVersionManagementDao.selectById(packageId);
        CommonServiceUtils.isEffectiveObj(entity, packageId);
        if (StringUtils.isBlank(entity.getPackageName()) || StringUtils.isBlank(entity.getVersionNumber())) {
            return R.ResponseError(400, "升级包信息丢失");
        }
        Map<String, String> serialNumberIpMap = new HashMap<>();
        for (String serialNumber : form.getSerialNumberList()) {
            Optional<String> optional = baseDeviceInscreenDao.getDeviceIpBySerialNumber(serialNumber).stream().filter(StringUtils::isNotBlank).findFirst();
            if (!optional.isPresent()) {
                return R.ResponseError(400, String.format("设备[%s]ip信息配置丢失", serialNumber));
            }
            String ip = optional.get();
            serialNumberIpMap.put(serialNumber, ip);
        }
        for (String serialNumber : form.getSerialNumberList()) {
            String ip = serialNumberIpMap.get(serialNumber);
            int packageType = "apk".equals(form.getUpgradeType()) ? 1 : 2;
            String updateVer = entity.getVersionNumber();
            String downLoadUrl = this.getPackageUrl(entity.getPackageName());
            terminalService.updateApp(ip, packageType, updateVer, downLoadUrl);
        }
//region 通过websocket方式通知
        /*
        List<String> serialNumberList = form.getSerialNumberList();
        List<String> clientIds = socketService.getSessionBySerialNumber(serialNumberList);
        if (clientIds.isEmpty()) {
            return R.ResponseError(400, "设备不在线");
        }
        PushMessageForm pushFrom = new PushMessageForm(SocketActionConstants.PushMessageTargetEnum.android.name(), SocketActionConstants.upgrade);
        pushFrom.setTerminal(null);
        pushFrom.setSerialNumbers(serialNumberList);
        pushFrom.setSessionIds(clientIds);
        Map<String, Object> params = new HashMap<>();
        params.put("upgradeType", form.getUpgradeType());
        params.put("upgradeVersion", entity.getVersionNumber());
        params.put("packageUrl", this.getPackageUrl(entity.getPackageName()));
        pushFrom.params(params);
        socketService.pushMessageToClient(pushFrom);
        */
//endregion
        return R.ResponseOk();
    }

    /**
     * 根据服务名获取服务ip端口
     *
     * @param serviceId
     * @return
     */
    public List<String> getServiceInstances(String serviceId) {
        List<String> instances = new ArrayList<>();
        List<ServiceInstance> serviceInstances = discoveryClient.getInstances(serviceId);
        for (ServiceInstance serviceInstance : serviceInstances) {
            instances.add(serviceInstance.getHost() + ":" + serviceInstance.getPort());
        }
        return instances;
    }
}
