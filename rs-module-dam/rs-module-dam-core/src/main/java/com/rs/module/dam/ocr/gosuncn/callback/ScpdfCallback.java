package com.rs.module.dam.ocr.gosuncn.callback;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.StringUtil;
import com.rs.module.dam.constant.DamConstants;
import com.rs.module.dam.constant.OcrConstants;
import com.rs.module.dam.entity.archive.ArchiveCoverDO;
import com.rs.module.dam.entity.archive.ArchiveJobDO;
import com.rs.module.dam.entity.log.IspInvokingDO;
import com.rs.module.dam.entity.sys.ConfigGroupDO;
import com.rs.module.dam.ocr.gosuncn.GoOcrCallbackParam;
import com.rs.module.dam.ocr.gosuncn.GoOcrCallbackParam.SerDatas;
import com.rs.module.dam.ocr.gosuncn.GoOcrHttpStatus;
import com.rs.module.dam.ocr.gosuncn.GoOcrStrategy;
import com.rs.module.dam.ocr.gosuncn.GoUtil;
import com.rs.module.dam.ocr.util.OcrUtil;
import com.rs.module.dam.service.archive.ArchiveCoverService;
import com.rs.module.dam.service.archive.ArchiveJobService;
import com.rs.module.dam.service.log.IspInvokingService;
import com.rs.module.dam.service.sys.ConfigGroupService;
import com.rs.module.dam.util.IpUtil;
import com.rs.module.dam.util.PdfGenerateUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * gosuncn-高新兴智能编目回调处理
 * <AUTHOR>
 * @date 2025年4月27日
 */
@Component
@Slf4j
public class ScpdfCallback implements IOcrCallback{    
    
    @Autowired
    private ConfigGroupService configGroupService;
	
	@Autowired
	private IspInvokingService ispInvokingService;
	
	@Autowired
    private ArchiveCoverService dmsArchiveCoverService;
	
	@Autowired
	private ArchiveJobService archiveJobService;
	
	@Autowired
    GoOcrStrategy goOcrStrategy;
	
	@Value("${conf.dam.urlMapping:}")
	private String urlMapping;

	/**
	 * 回调处理
	 * @param params GoOcrCallbackParam 回调参数
	 */
	@Override
	public void handle(GoOcrCallbackParam params) {
		
		//服务返回数据
		SerDatas serDatas = params.getSerDatas();
		
		//构建isp调用对象
		IspInvokingDO ispInvoking = new IspInvokingDO();
		ispInvoking.setId(StringUtil.getGuid32());
		ispInvoking.setJobId(serDatas.getBatchId());
		ispInvoking.setInput(JSON.toJSONString(params));
		ispInvoking.setSerCode(params.getSerCode());
		ispInvoking.setInvokeType(OcrConstants.OCR_INVOKE_TYPE_SCPDFZH_CALLBACK);
		ispInvoking.setRequestFrom(OcrConstants.OCR_REQUEST_FROM_ZNBM);
		ispInvoking.setServerIp(IpUtil.getHostIp());
		ispInvoking.setCheckType(OcrConstants.OCR_INVOKE_TYPE_SCPDFZH_CALLBACK);
		
		//开始调用时间
		long begin = System.currentTimeMillis();
		
		try {
			String scPdfUrl = GoUtil.getOcrResultsString(serDatas);
			scPdfUrl = OcrUtil.handleUrlMapping(scPdfUrl, urlMapping);
			log.info("ocr处理-获取双层pdf回调地址映射，源地址：{}, 映射地址：{}", GoUtil.getOcrResultsString(serDatas), scPdfUrl);
			if(StringUtil.isNotEmpty(scPdfUrl)) {
				
				//查找关联的任务
				ArchiveJobDO dmsArchiveJob = archiveJobService.getById(serDatas.getBatchId());
				
				//查询卷宗配置信息
				ConfigGroupDO dmsConfigGroup = configGroupService.getUserConfigGroup(dmsArchiveJob.getOrgCode());
				
				//构建isp调用对象
				ispInvoking.setAddress(dmsConfigGroup.getOcrServer());
				ispInvoking.setJgrybm(dmsArchiveJob.getJgrybm());
				
				//pdf成功生成(没有失败的文件)
				if(serDatas.getCode() == GoOcrHttpStatus.OK.getCode()) {
					dmsArchiveJob.setState(DamConstants.JOB_STATE_YES);
                    dmsArchiveJob.setEndTime(new Date());
                    dmsArchiveJob.setCoverContent(new JSONArray().toJSONString());
                    
                    //更新双层pdf获取任务
                    archiveJobService.updateById(dmsArchiveJob);
					
                    //将文件上传到存储服务器
                    JSONObject jsonObject = PdfGenerateUtil.upload(scPdfUrl);
                    log.info("ocr处理-获取双层pdf回调上传，上传结果：{}", jsonObject.toJSONString());
                    if (null != jsonObject){
                        scPdfUrl = jsonObject.getString("url");
                    }
				}
				
				ArchiveCoverDO dmsArchiveCover = new ArchiveCoverDO();
                dmsArchiveCover.setId(serDatas.getBatchId());
                dmsArchiveCover.setDoublePdfUrl(scPdfUrl);
                dmsArchiveCoverService.updateById(dmsArchiveCover);
			}
			
			//更新isp调用对象
			ispInvoking.setOutput("获取双层pdf回调成功");
		}
		catch (Exception e) {
			ispInvoking.setOutput(e.getMessage());
			ispInvoking.setRequestStatus("2");
			log.error("ocr处理-获取双层pdf回调异常：{}", e);
		}
		finally {
			
			//插入isp调用对象
			ispInvoking.setRequestTime(System.currentTimeMillis() - begin);
			ispInvoking.setAddTime(new Date());
			ispInvokingService.save(ispInvoking);
		}
	}
}
