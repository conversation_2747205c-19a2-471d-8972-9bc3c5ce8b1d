package com.rs.third.api.dto.haikang.tvwall;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.util.List;

/**
 * 监控点分页查询结果DTO
 */
@Data
public class CameraPointDTO {
    @JSONField(name = "total")
    private Integer total; // 总条数

    @JSONField(name = "pagesize")
    private Integer pageSize; // 当前分页记录数

    @JSONField(name = "pageNo")
    private Integer pageNo; // 当前页

    @JSONField(name = "list")
    private List<CameraInfo> list; // 监控点列表

    /**
     * 监控点详细信息
     */
    @Data
    public static class CameraInfo {
        @JSONField(name = "cameraindexCode")
        private String cameraIndexCode; // 监控点编号（通用唯一识别码UUID）

        @JSONField(name = "ghindexCode")
        private String ghIndexCode; // 监控点图标编号

        @JSONField(name = "name")
        private String name; // 监控点名称

        @JSONField(name = "deviceIndexCode")
        private String deviceIndexCode; // 所属设备编号（通用唯一识别码UUID）

        @JSONField(name = "longitude")
        private String longitude; // 经度（WGS84坐标系）

        @JSONField(name = "latitude")
        private String latitude; // 纬度（WGS84坐标系）

        @JSONField(name = "altitude")
        private String altitude; // 海拔高度（WGS84坐标系，单位：米）

        @JSONField(name = "pixel")
        private Integer pixel; // 摄像机像素（1-普通像素，2-130万高清，3-200万高清，4-300万高清）

        @JSONField(name = "camerafype")
        private Integer cameraType; // 监控点类型（0-枪机,1-半球,2-快球,3-带云台枪机）

        @JSONField(name = "camerafypeName")
        private String cameraTypeName; // 监控点类型说明

        @JSONField(name = "installplace")
        private String installPlace; // 安装位置

        @JSONField(name = "matrixCode")
        private String matrixCode; // 矩阵编号

        @JSONField(name = "chanNum")
        private Integer chanNum; // 通道号

        @JSONField(name = "viewRed")
        private String viewRed; // 可视域相关（JSON格式）

        @JSONField(name = "capabilitySet")
        private String capabilitySet; // 能力集

        @JSONField(name = "capabilitySetName")
        private String capabilitySetName; // 能力集说明

        @JSONField(name = "intelligentSet")
        private String intelligentSet; // 智能分析能力集

        @JSONField(name = "intelligentSetName")
        private String intelligentSetName; // 智能分析能力集说明

        @JSONField(name = "recordLocation")
        private String recordLocation; // 录像存储位置（0-中心存储，1-设备存储）

        @JSONField(name = "recordLocationName")
        private String recordLocationName; // 录像存储位置说明

        @JSONField(name = "pycController")
        private Integer pycController; // 云台控制（1-pvp,2-模拟矩阵,3-MM4000,4-NC600）

        @JSONField(name = "pycControllerName")
        private String pycControllerName; // 云台控制说明

        @JSONField(name = "deviceResourceType")
        private String deviceResourceType; // 所属设备类型

        @JSONField(name = "deviceResourceTypeName")
        private String deviceResourceTypeName; // 所属设备类型说明

        @JSONField(name = "channelType")
        private String channelType; // 通道子类型

        @JSONField(name = "channelTypeName")
        private String channelTypeName; // 通道子类型说明

        @JSONField(name = "transType")
        private Integer transType; // 传输协议（0-DDP,1-TCP）

        @JSONField(name = "transTypeName")
        private String transTypeName; // 传输协议类型说明

        @JSONField(name = "updateTime")
        private String updateTime; // 监控点更新时间（Iso8601格式yyyy-MM-dd'T'HH:mm:ss.888XXX）

        @JSONField(name = "unitIndexCode")
        private String unitIndexCode; // 所属组织编号（通用唯一识别码UUID）

        @JSONField(name = "treatyType")
        private String treatyType; // 接入协议

        @JSONField(name = "treatyTypeName")
        private String treatyTypeName; // 接入协议类型说明

        @JSONField(name = "createTime")
        private String createTime; // 监控点创建时间（Iso8601格式yyyy-MM-dd'T'HH:mm:ss.888XXX）

        @JSONField(name = "status")
        private Integer status; // 在线状态（0-不在线，1-在线）

        @JSONField(name = "statusName")
        private String statusName; // 状态说明
    }
}
