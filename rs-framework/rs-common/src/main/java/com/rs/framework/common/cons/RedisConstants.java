package com.rs.framework.common.cons;

/**
 * Redis常量类
 * <AUTHOR>
 * @date 2025年7月5日
 */
public class RedisConstants {

	/** 字典缓存，值为{@value} */
    public static final String DIC_CODE_CACHE_KEY = "com:dic";

    /** 医疗药品缓存，值为{@value} */
    public static final String MEDICINE_DATA_CACHE_KEY = "ihc:pm:medicine";

    /** 客户端认证缓存，值为{@value} */
    public static final String CLIENT_CREDENTIALS_EXPIRES = "rs:client_credentials_expires";

    /** bsp存储token目录，值为{@value} */
    public static final String BSP_ACCESS_TOKNE = "access:";

    /*********************【数据变更事件任务锁相关】*********************/

    /** 处理待处理事件的任务锁，值为{@value} */
    public static final String DATA_CHANGE_EVENT_PROCESSING_PENDING_LOCK = "acp:lock:processing_pending";

    /** 处理重试事件的任务锁，值为{@value} */
    public static final String DATA_CHANGE_EVENT_PROCESSING_RETRY_LOCK = "acp:lock:processing_retry";

    /** 清理过期事件的任务锁，值为{@value} */
    public static final String DATA_CHANGE_EVENT_CLEANING_EXPIRED_LOCK = "acp:lock:cleaning_expired";
}
