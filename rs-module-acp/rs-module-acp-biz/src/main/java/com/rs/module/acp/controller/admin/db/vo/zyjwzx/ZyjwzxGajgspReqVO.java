package com.rs.module.acp.controller.admin.db.vo.zyjwzx;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-暂予监外执行新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ZyjwzxGajgspReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("公安机关审批报请日期")
    private Date gajgspbqrq;

    @ApiModelProperty("审批机关名称")
    private String spjgmc;

    @ApiModelProperty("公安机关审批结果（1、同意；2、不同意。）")
    private String gajgspjg;

    @ApiModelProperty("公安机关审批备注")
    private String gajgspbz;

    @ApiModelProperty("公安机关审批材料")
    private String gajgspcl;


}
