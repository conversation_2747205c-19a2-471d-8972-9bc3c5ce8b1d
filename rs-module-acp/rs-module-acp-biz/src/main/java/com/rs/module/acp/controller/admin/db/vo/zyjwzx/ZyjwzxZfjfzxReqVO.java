package com.rs.module.acp.controller.admin.db.vo.zyjwzx;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-收押业务-暂予监外执行新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ZyjwzxZfjfzxReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("批准后是否在公安机关网站公开（1、是；2、否。）")
    private String pzhsfzgajgwzgk;

    @ApiModelProperty("批准后在公安机关网站公开日期")
    private Date pzhzgajgwzgkrq;

    @ApiModelProperty("材料送达看守所日期")
    private Date clsdkssrq;

    @ApiModelProperty("执行地与服刑地是否在同一省份（1、是；2、否。）")
    private String zxdyfxdsfztysf;

    @ApiModelProperty("执行地社区矫正机构名称")
    private String zxdsqjzjgmc;

    @ApiModelProperty("执行地接收罪犯档案看守所代码")
    private String zxdjszfdakssdm;

    @ApiModelProperty("执行地接收罪犯档案看守所名称")
    private String zxdjszfdakssmc;

}
